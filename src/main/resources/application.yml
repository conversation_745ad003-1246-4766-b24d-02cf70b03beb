server:
  port: 8080

spring:
  application:
    name: galaxy-boot-webflux-example

galaxy:
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: true
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true
  ai:
    enabled: true
    base-url: http://copilot.tsolph.chinastock.com.cn
    system-id: sys_cnf
    system-secret: VsJjm+PmoFn+VjF6-6bI1R9yU9ornxJ5
    account: shixiaolong_it
    app-id: app-5D55owR6HjNVPfCvsaYQKJ
    connect-timeout: 10000
    read-timeout: 60000
    write-timeout: 60000

logging:
  level:
    com.ctrip.framework: OFF