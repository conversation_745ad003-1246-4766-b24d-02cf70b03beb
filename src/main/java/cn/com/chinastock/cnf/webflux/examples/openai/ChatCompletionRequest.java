package cn.com.chinastock.cnf.webflux.examples.openai;

import com.alibaba.fastjson2.annotation.JSONField;

import java.util.List;

/**
 * OpenAI Chat Completion 请求模型
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class ChatCompletionRequest {
    
    /**
     * 模型名称
     */
    @JSONField(name = "model")
    private String model;

    /**
     * 消息列表
     */
    @JSONField(name = "messages")
    private List<ChatMessage> messages;

    /**
     * 是否流式响应
     */
    @JSONField(name = "stream")
    private Boolean stream = false;

    /**
     * 温度参数，控制随机性
     */
    @JSONField(name = "temperature")
    private Double temperature;

    /**
     * 最大 token 数
     */
    @JSONField(name = "max_tokens")
    private Integer maxTokens;

    /**
     * top_p 参数
     */
    @JSONField(name = "top_p")
    private Double topP;

    /**
     * 频率惩罚
     */
    @JSONField(name = "frequency_penalty")
    private Double frequencyPenalty;

    /**
     * 存在惩罚
     */
    @JSONField(name = "presence_penalty")
    private Double presencePenalty;

    /**
     * 停止词
     */
    @JSONField(name = "stop")
    private List<String> stop;

    /**
     * 用户标识
     */
    @JSONField(name = "user")
    private String user;
    
    public ChatCompletionRequest() {
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public List<ChatMessage> getMessages() {
        return messages;
    }
    
    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }
    
    public Boolean getStream() {
        return stream;
    }
    
    public void setStream(Boolean stream) {
        this.stream = stream;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Integer getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public Double getTopP() {
        return topP;
    }
    
    public void setTopP(Double topP) {
        this.topP = topP;
    }
    
    public Double getFrequencyPenalty() {
        return frequencyPenalty;
    }
    
    public void setFrequencyPenalty(Double frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }
    
    public Double getPresencePenalty() {
        return presencePenalty;
    }
    
    public void setPresencePenalty(Double presencePenalty) {
        this.presencePenalty = presencePenalty;
    }
    
    public List<String> getStop() {
        return stop;
    }
    
    public void setStop(List<String> stop) {
        this.stop = stop;
    }
    
    public String getUser() {
        return user;
    }
    
    public void setUser(String user) {
        this.user = user;
    }
    
    @Override
    public String toString() {
        return "ChatCompletionRequest{" +
                "model='" + model + '\'' +
                ", messages=" + messages +
                ", stream=" + stream +
                ", temperature=" + temperature +
                ", maxTokens=" + maxTokens +
                ", topP=" + topP +
                ", frequencyPenalty=" + frequencyPenalty +
                ", presencePenalty=" + presencePenalty +
                ", stop=" + stop +
                ", user='" + user + '\'' +
                '}';
    }
}
