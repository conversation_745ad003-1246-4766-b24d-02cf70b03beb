package cn.com.chinastock.cnf.webflux.examples;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Galaxy Boot WebFlux 示例应用启动类
 * 
 * <p>展示如何使用 galaxy-boot-starter-webflux 来构建响应式 Web 应用</p>
 * 
 */
@SpringBootApplication
public class GalaxyBootPlaybookWebfluxApplication {

    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(GalaxyBootPlaybookWebfluxApplication.class, args);
    }
} 