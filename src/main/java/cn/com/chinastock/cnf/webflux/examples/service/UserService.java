package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.exception.BusinessException;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.model.User;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户服务类
 * 
 * <p>简化的响应式用户服务</p>
 * 
 * <AUTHOR> Boot Team
 * @version 1.0
 * @since 1.0
 */
@Service
public class UserService {
    
    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UserService.class);
    
    // 模拟数据存储
    private final ConcurrentHashMap<Long, User> userStorage = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    /**
     * 构造函数，初始化测试数据
     */
    public UserService() {
        initTestData();
    }
    
    /**
     * 获取所有用户
     * 
     * @return 用户流
     */
    public Flux<User> getAllUsers() {
        logger.info("从存储中获取所有用户");
        return Flux.fromIterable(userStorage.values())
                .delayElements(Duration.ofMillis(10)) // 模拟数据库延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> logger.debug("开始获取用户列表"))
                .doOnComplete(() -> logger.debug("用户列表获取完成"));
    }
    
    /**
     * 根据ID获取用户
     * 
     * @param id 用户ID
     * @return 用户信息的Mono
     */
    public Mono<User> getUserById(Long id) {
        logger.info("根据ID获取用户: {}", id);
        return Mono.fromCallable(() -> userStorage.get(id))
                .delayElement(Duration.ofMillis(50)) // 模拟数据库查询延迟  
                .subscribeOn(Schedulers.boundedElastic())
                .switchIfEmpty(Mono.error(new BusinessException("1001", "用户不存在: " + id)))
                .doOnNext(user -> logger.debug("成功获取用户: {}", user.getUsername()))
                .doOnError(error -> logger.error("获取用户失败: {}", error.getMessage()));
    }
    
    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 创建后的用户信息Mono
     */
    public Mono<User> createUser(User user) {
        logger.info("创建用户: {}", user.getUsername());
        return Mono.fromCallable(() -> {
            if (userStorage.values().stream().anyMatch(u -> u.getUsername().equals(user.getUsername()))) {
                throw new BusinessException("1002", "用户名已存在: " + user.getUsername());
            }
            
            Long id = idGenerator.getAndIncrement();
            user.setId(id);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            userStorage.put(id, user);
            return user;
        })
        .delayElement(Duration.ofMillis(100)) // 模拟数据库写入延迟
        .subscribeOn(Schedulers.boundedElastic())
        .doOnNext(savedUser -> logger.info("用户创建成功: {}", savedUser.getUsername()))
        .doOnError(error -> logger.error("用户创建失败: {}", error.getMessage()));
    }
    
    /**
     * 更新用户信息
     * 
     * @param id 用户ID
     * @param userUpdate 更新的用户信息
     * @return 更新后的用户信息Mono
     */
    public Mono<User> updateUser(Long id, User userUpdate) {
        logger.info("更新用户: {}", id);
        return Mono.fromCallable(() -> {
            User existingUser = userStorage.get(id);
            if (existingUser == null) {
                throw new BusinessException("1001", "用户不存在: " + id);
            }
            
            if (userUpdate.getUsername() != null) {
                existingUser.setUsername(userUpdate.getUsername());
            }
            if (userUpdate.getEmail() != null) {
                existingUser.setEmail(userUpdate.getEmail());
            }
            if (userUpdate.getRealName() != null) {
                existingUser.setRealName(userUpdate.getRealName());
            }
            if (userUpdate.getStatus() != null) {
                existingUser.setStatus(userUpdate.getStatus());
            }
            
            existingUser.setUpdateTime(LocalDateTime.now());
            return existingUser;
        });
    }
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 删除操作的Mono
     */
    public Mono<Void> deleteUser(Long id) {
        logger.info("删除用户: {}", id);
        return Mono.fromRunnable(() -> {
            User removedUser = userStorage.remove(id);
            if (removedUser == null) {
                throw new BusinessException("1001", "用户不存在: " + id);
            }
        });
    }
    
    /**
     * 搜索用户
     * 
     * @param username 用户名关键字
     * @param page 页码
     * @param size 页大小
     * @return 搜索结果的Flux
     */
    public Flux<User> searchUsers(String username, int page, int size) {
        logger.info("搜索用户: username={}, page={}, size={}", username, page, size);
        return Flux.fromIterable(userStorage.values())
                .filter(user -> username == null || user.getUsername().contains(username))
                .skip((long) page * size)
                .take(size)
                .delayElements(Duration.ofMillis(5)) // 模拟搜索延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> logger.debug("开始搜索用户"))
                .doOnComplete(() -> logger.debug("用户搜索完成"));
    }
    
    /**
     * 初始化测试数据
     */
    private void initTestData() {
        logger.info("初始化测试数据");
        
        User user1 = new User("admin", "admin123", "<EMAIL>");
        user1.setId(1L);
        user1.setRealName("管理员");
        user1.setPhoneNumber("13800138000");
        user1.setStatus(User.UserStatus.ACTIVE);
        
        User user2 = new User("john", "password123", "<EMAIL>");
        user2.setId(2L);
        user2.setRealName("约翰");
        user2.setPhoneNumber("13900139000");
        user2.setStatus(User.UserStatus.ACTIVE);
        
        userStorage.put(1L, user1);
        userStorage.put(2L, user2);
        
        logger.info("测试数据初始化完成，共 {} 个用户", userStorage.size());
        idGenerator.set(3L);
    }
} 