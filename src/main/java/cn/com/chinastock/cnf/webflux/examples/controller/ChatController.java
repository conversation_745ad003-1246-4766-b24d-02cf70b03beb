package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.openai.ChatCompletionRequest;
import cn.com.chinastock.cnf.webflux.examples.openai.ChatMessage;
import cn.com.chinastock.cnf.webflux.examples.service.ChatGlmService;
import cn.com.chinastock.cnf.webflux.examples.service.GalaxyAiService;
import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * OpenAI 兼容的聊天接口控制器
 *
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@CrossOrigin(origins = "*")
public class ChatController {
    @Autowired
    private GalaxyAiService galaxyAiService;

    @Autowired
    private ChatGlmService chatGlmService;

    /**
     * OpenAI 兼容的聊天完成接口
     */
    @PostMapping("/v1/chat/completions")
    public Object chatCompletions(@RequestBody ChatCompletionRequest request) {
        try {
            // 验证请求
            validateRequest(request);

            // 如果是流式请求
            if (Boolean.TRUE.equals(request.getStream())) {
                return handleStreamRequest(request);
            } else {
                return handleNonStreamRequest(request);
            }

        } catch (IllegalArgumentException e) {
            GalaxyLogger.warn("Invalid request: {}", e.getMessage());
            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request", e.getMessage());
        } catch (Exception e) {
            GalaxyLogger.error("Error processing chat completion request", e);
            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error", "Internal server error");
        }
    }

    /**
     * 处理流式请求
     */
    private Flux<ServerSentEvent<String>> handleStreamRequest(ChatCompletionRequest request) {
        return galaxyAiService.chatStream(request.getMessages(), generateConversationId())
                .map(data -> ServerSentEvent.<String>builder()
                        .data(data)
                        .build())
                .doOnError(error -> GalaxyLogger.error("Error in stream chat", error))
                .onErrorResume(error -> {
                    // 发送错误信息作为 SSE 事件
                    Map<String, Object> errorResponse = createErrorMap("internal_error", "Internal server error");
                    String errorJson = JSON.toJSONString(errorResponse);
                    return Flux.just(ServerSentEvent.<String>builder()
                            .data(errorJson)
                            .build());
                });
    }

    /**
     * 处理非流式请求
     */
    private ResponseEntity<Object> handleNonStreamRequest(ChatCompletionRequest request) {
        // 对于非流式请求，我们暂时返回一个错误，因为 Galaxy AI 主要支持流式
        Map<String, Object> error = new HashMap<>();
        error.put("error", Map.of(
                "message", "Non-streaming requests are not currently supported. Please set 'stream': true",
                "type", "not_supported",
                "code", "non_stream_not_supported"
        ));

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(ChatCompletionRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request body is required");
        }

        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new IllegalArgumentException("Messages are required");
        }

        // 验证消息格式
        for (ChatMessage message : request.getMessages()) {
            if (message.getRole() == null || message.getRole().trim().isEmpty()) {
                throw new IllegalArgumentException("Message role is required");
            }

            if (message.getContent() == null || message.getContent().trim().isEmpty()) {
                throw new IllegalArgumentException("Message content is required");
            }

            // 验证角色类型
            String role = message.getRole().toLowerCase();
            if (!"system".equals(role) && !"user".equals(role) && !"assistant".equals(role)) {
                throw new IllegalArgumentException("Invalid message role: " + message.getRole());
            }
        }
    }

    /**
     * 生成会话 ID
     */
    private String generateConversationId() {
        return "chat-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * ChatGLM 测试接口 - 用于验证 SSE 流式响应
     */
    @PostMapping(value = "/test/chat/completions")
    public Flux<ServerSentEvent<String>> testChatGlm(@RequestBody ChatCompletionRequest request) {
        return chatGlmService.chatStream(request.getMessages())
                .map(data -> ServerSentEvent.<String>builder()
                        .data(data)
                        .build())
                .doOnError(error -> GalaxyLogger.error("Error in ChatGLM stream", error))
                .onErrorResume(error -> {
                    // 发送错误信息作为 SSE 事件
                    Map<String, Object> errorResponse = createErrorMap("internal_error", "Internal server error");
                    String errorJson = JSON.toJSONString(errorResponse);
                    return Flux.just(ServerSentEvent.<String>builder()
                            .data(errorJson)
                            .build());
                });
    }

    /**
     * 创建错误响应
     */
    private ResponseEntity<Object> createErrorResponse(HttpStatus status, String type, String message) {
        Map<String, Object> error = createErrorMap(type, message);
        return ResponseEntity.status(status).body(error);
    }

    /**
     * 创建错误 Map
     */
    private Map<String, Object> createErrorMap(String type, String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("error", Map.of(
                "message", message,
                "type", type,
                "code", type
        ));
        return error;
    }
}
