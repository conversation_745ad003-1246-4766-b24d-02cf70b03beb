package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.openai.ChatMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ChatGLM 测试服务
 * 用于验证 SSE 流式响应功能
 *
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Service
public class ChatGlmService {
    private static final String CHATGLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4";
    private static final String CHATGLM_MODEL = "glm-4-air";
    private static final String CHATGLM_API_KEY = "850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj";

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public ChatGlmService() {
        // 初始化 HTTP 客户端，增加超时时间用于调试
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)  // 增加读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();

        GalaxyLogger.info("ChatGLM test service initialized");
    }

    /**
     * 发起 ChatGLM 聊天请求（流式）
     */
    public Flux<String> chatStream(List<ChatMessage> messages) {
        return Flux.create(sink -> {
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("model", CHATGLM_MODEL);
            requestData.put("messages", messages);
            requestData.put("stream", true);
            requestData.put("temperature", 0.1);

            String requestJson;
            try {
                requestJson = objectMapper.writeValueAsString(requestData);
            } catch (Exception e) {
                sink.error(new RuntimeException("Failed to serialize request data", e));
                return;
            }

            Request request = new Request.Builder()
                    .url(CHATGLM_BASE_URL + "/chat/completions")
                    .header("Authorization", "Bearer " + CHATGLM_API_KEY)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(
                            requestJson,
                            okhttp3.MediaType.get("application/json")
                    ))
                    .build();

            GalaxyLogger.info("Sending request to ChatGLM API...");

            EventSource eventSource = EventSources.createFactory(httpClient).newEventSource(request, new EventSourceListener() {
                @Override
                public void onOpen(EventSource eventSource, Response response) {
                    if (!response.isSuccessful()) {
                        try {
                            response.body(); // consume the body to avoid resource leak
                        } catch (Exception e) {
                            // ignore
                        }
                        sink.error(new RuntimeException("ChatGLM API error: " + response.code()));
                    }
                }

                @Override
                public void onEvent(EventSource eventSource, String id, String type, String data) {
                    try {
                        if ("[DONE]".equals(data)) {
                            sink.complete();
                            return;
                        }

                        if (data != null && !data.trim().isEmpty()) {
                            sink.next(data);
                        }
                    } catch (Exception e) {
                        GalaxyLogger.error("Error processing ChatGLM SSE event", e);
                        sink.error(e);
                    }
                }

                @Override
                public void onClosed(EventSource eventSource) {
                    sink.complete();
                }

                @Override
                public void onFailure(EventSource eventSource, Throwable t, Response response) {
                    GalaxyLogger.error("ChatGLM SSE connection failed", t);
                    if (response != null) {
                        GalaxyLogger.error("Response code: {}, message: {}", response.code(), response.message());
                        try {
                            String errorBody = response.body() != null ? response.body().string() : "No error body";
                            GalaxyLogger.error("Error response body: {}", errorBody);
                        } catch (Exception e) {
                            GalaxyLogger.error("Failed to read error response body", e);
                        }
                    }
                    sink.error(t);
                }
            });

            // 当 Flux 被取消时，关闭 EventSource
            sink.onCancel(() -> {
                if (eventSource != null) {
                    eventSource.cancel();
                }
            });
        });
    }
}
