package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.openai.ChatMessage;
import com.alibaba.fastjson2.JSON;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ChatGLM 测试服务
 * 用于验证 SSE 流式响应功能
 *
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Service
public class ChatGlmService {
    private static final String CHATGLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4";
    private static final String CHATGLM_MODEL = "glm-4-air";
    private static final String CHATGLM_API_KEY = "850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj";

    private final OkHttpClient httpClient;

    public ChatGlmService() {
        // 初始化 HTTP 客户端，增加超时时间用于调试
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)  // 增加读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        GalaxyLogger.info("ChatGLM test service initialized");
    }

    /**
     * 发起 ChatGLM 聊天请求（流式）
     */
    public void chatStream(List<ChatMessage> messages, SseEmitter emitter) throws Exception {
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("model", CHATGLM_MODEL);
        requestData.put("messages", messages);
        requestData.put("stream", true);
        requestData.put("temperature", 0.1);

        String requestJson = JSON.toJSONString(requestData);

        Request request = new Request.Builder()
                .url(CHATGLM_BASE_URL + "/chat/completions")
                .header("Authorization", "Bearer " + CHATGLM_API_KEY)
                .header("Accept", "text/event-stream")
                .header("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestJson,
                        okhttp3.MediaType.get("application/json")
                ))
                .build();

        GalaxyLogger.info("Sending request to ChatGLM API...");

        EventSources.createFactory(httpClient).newEventSource(request, new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, Response response) {
//                GalaxyLogger.info("ChatGLM SSE connection opened, response code: {}", response.code());
                if (!response.isSuccessful()) {
//                    GalaxyLogger.error("ChatGLM API returned error: {} {}", response.code(), response.message());
                    try {
                        String errorBody = response.body() != null ? response.body().string() : "No error body";
//                        GalaxyLogger.error("Error response body: {}", errorBody);
                    } catch (Exception e) {
//                        GalaxyLogger.error("Failed to read error response body", e);
                    }
                    emitter.completeWithError(new RuntimeException("ChatGLM API error: " + response.code()));
                }
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                try {
                    if ("[DONE]".equals(data)) {
                        emitter.complete();
                        return;
                    }

                    if (data != null && !data.trim().isEmpty()) {
                        emitter.send(SseEmitter.event().data(data));
                    }
                } catch (Exception e) {
                    GalaxyLogger.error("Error processing ChatGLM SSE event", e);
                    emitter.completeWithError(e);
                }
            }

            @Override
            public void onClosed(EventSource eventSource) {
                try {
                    emitter.complete();
                } catch (Exception e) {
//                    GalaxyLogger.warn("Error completing emitter on close", e);
                }
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                GalaxyLogger.error("ChatGLM SSE connection failed", t);
                if (response != null) {
                    GalaxyLogger.error("Response code: {}, message: {}", response.code(), response.message());
                    try {
                        String errorBody = response.body() != null ? response.body().string() : "No error body";
                        GalaxyLogger.error("Error response body: {}", errorBody);
                    } catch (Exception e) {
                        GalaxyLogger.error("Failed to read error response body", e);
                    }
                }
                emitter.completeWithError(t);
            }
        });
    }
}
