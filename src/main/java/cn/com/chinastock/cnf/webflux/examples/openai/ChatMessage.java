package cn.com.chinastock.cnf.webflux.examples.openai;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * OpenAI Chat Message 模型
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class ChatMessage {
    
    /**
     * 消息角色：system, user, assistant
     */
    @JSONField(name = "role")
    private String role;
    
    /**
     * 消息内容
     */
    @JSONField(name = "content")
    private String content;
    
    /**
     * 消息名称（可选）
     */
    @JSONField(name = "name")
    private String name;
    
    public ChatMessage() {
    }
    
    public ChatMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }
    
    public ChatMessage(String role, String content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String toString() {
        return "ChatMessage{" +
                "role='" + role + '\'' +
                ", content='" + content + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
